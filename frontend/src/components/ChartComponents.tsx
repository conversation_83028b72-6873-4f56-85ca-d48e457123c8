import { <PERSON><PERSON>olumn, ChartLine } from "lucide-react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>tipContent, TooltipProps } from "recharts"
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent"
import { TickItem } from "recharts/types/util/types"

import { CHART_TYPES, ChartType } from "@/types/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export const CUSTOM_CHART_COLORS = [
  "oklch(0.50 0.14 260)",
  "oklch(0.45 0.12 230)",
  "oklch(0.40 0.13 290)",
  "oklch(0.42 0.15 200)",
  "oklch(0.38 0.12 310)",
  "oklch(0.47 0.11 240)",
  "oklch(0.43 0.16 270)",
  "oklch(0.50 0.10 220)",
  "oklch(0.41 0.14 250)",
  "oklch(0.39 0.13 180)",
  "oklch(0.44 0.15 300)",
  "oklch(0.46 0.12 210)",
  "oklch(0.48 0.17 290)",
  "oklch(0.36 0.11 270)",
  "oklch(0.52 0.14 230)",
  "oklch(0.49 0.10 310)",
  "oklch(0.53 0.12 240)",
  "oklch(0.37 0.13 200)",
  "oklch(0.51 0.11 250)",
  "oklch(0.40 0.10 280)",
  "oklch(0.50 0.16 290)",
  "oklch(0.38 0.14 260)",
  "oklch(0.42 0.12 270)",
  "oklch(0.45 0.13 210)",
  "oklch(0.39 0.15 300)",
  "oklch(0.36 0.10 220)",
  "oklch(0.41 0.11 180)",
  "oklch(0.48 0.14 240)",
  "oklch(0.44 0.10 250)",
  "oklch(0.50 0.13 290)",
]

export const ChartTypeSelect = ({
  value,
  setValue,
  disabled,
}: {
  value: ChartType
  setValue: React.Dispatch<React.SetStateAction<ChartType>>
  disabled?: boolean
}) => (
  <Select
    value={value}
    onValueChange={(val) => setValue(val as ChartType)}
    disabled={disabled}
  >
    <SelectTrigger size="sm">
      <SelectValue placeholder="Select" />
    </SelectTrigger>
    <SelectContent>
      {CHART_TYPES.map((type) => (
        <SelectItem key={type} value={type}>
          {type === "Bar" ? (
            <ChartColumn className="inline size-3.5" />
          ) : (
            <ChartLine className="inline size-3.5" />
          )}
          {type}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
)

export const CustomSelect = ({
  value,
  setValue,
  options,
  disabled,
}: {
  value: string
  setValue: React.Dispatch<React.SetStateAction<string>>
  options: string[]
  disabled?: boolean
}) => (
  <Select
    value={value}
    onValueChange={(val) => setValue(val)}
    disabled={disabled}
  >
    <SelectTrigger size="sm">
      <SelectValue placeholder="Select" />
    </SelectTrigger>
    <SelectContent>
      {options.map((option) => (
        <SelectItem key={option} value={option}>
          {option}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
)

export const WrappedTick = ({
  x,
  y,
  payload,
}: {
  x?: number
  y?: number
  payload: TickItem & { value: ValueType }
}) => {
  const words = payload.value.split("<br/>")

  return (
    <g transform={`translate(${x},${(y || 0) + 10})`}>
      <text textAnchor="middle" fontSize={12} fill="var(--muted-foreground)">
        {words.map((word: string, index: number) => (
          <tspan key={index} x={0} dy={index === 0 ? 0 : 12}>
            {word}
          </tspan>
        ))}
      </text>
    </g>
  )
}

export const WrappedTooltip = (props: TooltipProps<ValueType, NameType>) => {
  if (!props.active) return null

  const label = String(props.label).replace("<br/>", " ")

  return <DefaultTooltipContent {...props} label={label} />
}
