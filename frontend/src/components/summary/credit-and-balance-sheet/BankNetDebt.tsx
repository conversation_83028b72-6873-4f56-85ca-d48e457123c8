"use client"

import React, { useState } from "react"
import {
  Bar,
  Bar<PERSON>hart,
  Cell,
  LabelList,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { BankNetDebtData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  ChartTypeSelect,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useProfitAndLossRevenue } from "@/services/summary"

const BankNetDebt = () => {
  const { isLoading } = useProfitAndLossRevenue()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  const data: BankNetDebtData[] = [
    {
      name: "2021",
      bankDebt: 16500,
      netDebt: 14500,
    },
    {
      name: "2022",
      bankDebt: 15500,
      netDebt: 12493,
    },
    {
      name: "2023",
      bankDebt: 18500,
      netDebt: 11500,
    },
    {
      name: "2024",
      bankDebt: 16500,
      netDebt: 14500,
    },
    {
      name: "YTD 2025",
      bankDebt: 16500,
      netDebt: 14500,
    },
  ]

  return (
    <Card
      title="Bank & Net Debt"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      isLoading={isLoading}
    >
      <BankNetDebtChart data={data} chartType={chartType} />
    </Card>
  )
}

export default BankNetDebt

const BankNetDebtChart = ({
  data,
  chartType,
}: {
  data: BankNetDebtData[]
  chartType: ChartType
}) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    {chartType === "Bar" ? (
      <BarChart data={data} margin={{ top: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Bar dataKey="bankDebt" name="Bank Debt" fill="var(--chart-1)">
          <Cell fill="var(--chart-1)" />

          <LabelList
            dataKey="bankDebt"
            position="center"
            fill="white"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Bar>

        <Bar dataKey="netDebt" name="Net Debt" fill="var(--chart-3)">
          <Cell fill="var(--chart-3)" />

          <LabelList
            dataKey="netDebt"
            position="center"
            fill="white"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Bar>
      </BarChart>
    ) : (
      <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Line dataKey="bankDebt" name="Bank Debt" stroke="var(--chart-1)">
          <LabelList
            dataKey="bankDebt"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Line>

        <Line dataKey="netDebt" name="Net Debt" stroke="var(--chart-3)">
          <LabelList
            dataKey="netDebt"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Line>
      </LineChart>
    )}
  </ResponsiveContainer>
)
