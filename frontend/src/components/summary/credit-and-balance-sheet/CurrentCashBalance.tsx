"use client"

import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Cell,
  LabelList,
  ResponsiveContainer,
  XAxis,
} from "recharts"

import { WaterfallChartData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { WrappedTick } from "@/components/ChartComponents"
import { useProfitAndLossRevenue } from "@/services/summary"

const CurrentCashBalance = () => {
  const { isLoading } = useProfitAndLossRevenue()

  const data: WaterfallChartData[] = [
    {
      name: "2024",
      amount: 11239,
      remaining: 0,
    },
    {
      name: "OCF",
      amount: 200,
      remaining: 11239,
    },
    {
      name: "CAPEX",
      amount: -3628,
      remaining: 11439,
    },
    {
      name: "Share<br/>Loans",
      amount: 710,
      remaining: 7811,
    },
    {
      name: "Bank<br/>Loan",
      amount: 1500,
      remaining: 8521,
    },
    {
      name: "Dividends",
      amount: -1000,
      remaining: 10021,
    },
    {
      name: "Others",
      amount: -1000,
      remaining: 9021,
    },
    {
      name: "YTD 2025",
      amount: 8021,
      remaining: 0,
    },
  ]

  return (
    <Card
      title="Current Cash Balance"
      flashNumberLabel="(excl. Vidaskin)"
      isLoading={isLoading}
    >
      <WaterfallChart data={data} />
    </Card>
  )
}

export default CurrentCashBalance

export const WaterfallChart = ({ data }: { data: WaterfallChartData[] }) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    <BarChart data={data} margin={{ top: 24 }}>
      <XAxis dataKey="name" tick={WrappedTick} interval={0} tickLine={false} />

      <Bar dataKey="remaining" stackId="a" fill="transparent" />

      <Bar dataKey="amount" name="Amount" stackId="a" fill="var(--chart-1)">
        {data.map((item, index) => {
          if (item.amount < 0) {
            return <Cell key={index} fill="var(--chart-3)" />
          }

          if (index === data.length - 1) {
            return <Cell key={index} fill="var(--chart-5)" />
          }

          return <Cell key={index} fill="var(--chart-1)" />
        })}

        <LabelList
          dataKey="amount"
          position="top"
          fill="var(--foreground)"
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
          fontSize={12}
        />
      </Bar>
    </BarChart>
  </ResponsiveContainer>
)
