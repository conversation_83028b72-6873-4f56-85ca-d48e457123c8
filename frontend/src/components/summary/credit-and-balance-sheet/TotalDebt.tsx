"use client"

import React, { useState } from "react"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { Card } from "@/components/CardComponents"
import { ChartTypeSelect } from "@/components/ChartComponents"
import { RevenueChart } from "@/components/summary/profit-and-loss/Revenue"
import { useProfitAndLossRevenue } from "@/services/summary"

const TotalDebt = () => {
  const { data, isLoading } = useProfitAndLossRevenue()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  return (
    <Card
      title="Total Debt"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      isLoading={isLoading}
    >
      <RevenueChart data={data} chartType={chartType} />
    </Card>
  )
}

export default TotalDebt
