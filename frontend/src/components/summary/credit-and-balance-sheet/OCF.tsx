"use client"

import React from "react"
import {
  <PERSON><PERSON>ist,
  <PERSON>,
  <PERSON><PERSON>hart,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  XA<PERSON><PERSON>,
} from "recharts"

import { OCFData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { WrappedTick, WrappedTooltip } from "@/components/ChartComponents"
import { useProfitAndLossRevenue } from "@/services/summary"

const OCF = () => {
  const { isLoading } = useProfitAndLossRevenue()

  const data: OCFData[] = [
    {
      name: "2021",
      amount: 10000000,
    },
    {
      name: "2022",
      amount: 25000000,
    },
    {
      name: "2023",
      amount: 40000000,
    },
    {
      name: "2024",
      amount: 10000000,
    },
    {
      name: "2025B",
      amount: 22000000,
    },
  ]

  return (
    <Card title="OCF" flashNumberLabel="(excl. Vidaskin)" isLoading={isLoading}>
      <OCFChart data={data} />
    </Card>
  )
}

export default OCF

const OCFChart = ({ data }: { data: OCFData[] }) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
      <XAxis dataKey="name" tick={WrappedTick} tickLine={false} interval={0} />

      <Tooltip
        content={<WrappedTooltip />}
        formatter={(value: number) => formatAbbreviatedCurrency(value)}
      />

      <Line dataKey="amount" name="Amount" stroke="var(--chart-1)">
        <LabelList
          dataKey="amount"
          position="top"
          fill="var(--foreground)"
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
          fontSize={12}
        />
      </Line>
    </LineChart>
  </ResponsiveContainer>
)
