"use client"

import React from "react"
import {
  <PERSON>,
  Compo<PERSON><PERSON><PERSON>,
  Label<PERSON>ist,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { PatientsData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { WrappedTick, WrappedTooltip } from "@/components/ChartComponents"
import { useProfitAndLossRevenue } from "@/services/summary"

const Patients = () => {
  const { isLoading } = useProfitAndLossRevenue()

  const data: PatientsData[] = [
    {
      name: "2021",
      barValue: 59000,
      lineValue: 10,
    },
    {
      name: "2022",
      barValue: 40000,
      lineValue: 27.2,
    },
    {
      name: "2023",
      barValue: 60000,
      lineValue: 30,
    },
    {
      name: "2024",
      barValue: 30000,
      lineValue: 10,
    },
    {
      name: "2025B",
      barValue: 45000,
      lineValue: 27.2,
    },
    {
      name: "2025",
      barValue: 55000,
      lineValue: 30,
    },
  ]

  const labels: Record<keyof PatientsData, string> = {
    name: "Period",
    barValue: "Patients",
    lineValue: "Revenue per patient",
  }

  return (
    <Card
      title="Patients"
      flashNumberLabel="(excl. Vidaskin)"
      isLoading={isLoading}
    >
      <PatientsChart data={data} labels={labels} />
    </Card>
  )
}

export default Patients

type PatientsDataWithLinePosition = PatientsData & {
  linePosition?: number
}

const addLinePosition = (
  data: PatientsData[]
): PatientsDataWithLinePosition[] => {
  const sums: number[] = data.map((entry) => entry.barValue)

  const maxSum = Math.max(...sums)

  return data.map((entry) => {
    if (!entry.lineValue) return entry

    const linePosition =
      maxSum + (entry.lineValue / 100) * maxSum + maxSum * 0.05

    return {
      ...entry,
      linePosition,
    }
  })
}

export const PatientsChart = ({
  data,
  labels,
}: {
  data: PatientsData[]
  labels: Record<keyof PatientsData, string>
}) => {
  const chartData = addLinePosition(data)

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart data={chartData} margin={{ top: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <YAxis orientation="left" hide />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Legend verticalAlign="bottom" wrapperStyle={{ fontSize: 12 }} />

        <Line
          dataKey="linePosition"
          name={labels.lineValue}
          stroke="var(--color-chart-3)"
        >
          <LabelList
            dataKey="lineValue"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Line>

        <Bar
          dataKey="barValue"
          name={labels.barValue}
          stackId="a"
          fill="var(--color-chart-1)"
        >
          <LabelList
            dataKey="barValue"
            position="center"
            fill="white"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Bar>
      </ComposedChart>
    </ResponsiveContainer>
  )
}
