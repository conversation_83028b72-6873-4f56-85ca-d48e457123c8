"use client"

import React from "react"

import { PatientsData } from "@/types/summary"
import { Card } from "@/components/CardComponents"
import { PatientsChart } from "@/components/summary/patients/Patients"
import { useProfitAndLossRevenue } from "@/services/summary"

const NewPatients = () => {
  const { isLoading } = useProfitAndLossRevenue()

  const data: PatientsData[] = [
    {
      name: "2021",
      barValue: 59000,
      lineValue: 10,
    },
    {
      name: "2022",
      barValue: 40000,
      lineValue: 27.2,
    },
    {
      name: "2023",
      barValue: 60000,
      lineValue: 30,
    },
    {
      name: "2024",
      barValue: 30000,
      lineValue: 10,
    },
    {
      name: "2025B",
      barValue: 45000,
      lineValue: 27.2,
    },
    {
      name: "2025",
      barValue: 55000,
      lineValue: 30,
    },
  ]

  const labels: Record<keyof PatientsData, string> = {
    name: "Period",
    barValue: "Patients",
    lineValue: "Revenue per patient",
  }

  return (
    <Card
      title="New Patients"
      flashNumberLabel="(excl. Vidaskin)"
      isLoading={isLoading}
    >
      <PatientsChart data={data} labels={labels} />
    </Card>
  )
}

export default NewPatients
