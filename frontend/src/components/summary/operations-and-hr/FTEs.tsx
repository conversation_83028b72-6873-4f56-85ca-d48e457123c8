"use client"

import React, { useState } from "react"

import { StackedBarData } from "@/types/summary"
import { Card } from "@/components/CardComponents"
import { CustomSelect } from "@/components/ChartComponents"
import { StackedBarChart } from "@/components/summary/operations-and-hr/Headcount"
import { useProfitAndLossRevenue } from "@/services/summary"

const OPTIONS = ["Default", "By Staff Type"]

const FTEs = () => {
  const { isLoading } = useProfitAndLossRevenue()
  const [viewType, setViewType] = useState<string>(OPTIONS[0])

  const data = [
    {
      name: "2021",
      Corporate: 35,
      PSA: 20,
      Clinic: 15,
      Nurse: 10,
      Doctor: 5,
    },
    {
      name: "2022",
      Corporate: 35,
      PSA: 30,
      Clinic: 10,
      Nurse: 30,
      Doctor: 5,
    },
    {
      name: "2023",
      Corporate: 35,
      PSA: 30,
      Clinic: 10,
      Nurse: 30,
      Doctor: 20,
    },
    {
      name: "2024",
      Corporate: 35,
      PSA: 20,
      Clinic: 15,
      Nurse: 10,
      Doctor: 10,
    },
    {
      name: "2025",
      Corporate: 35,
      PSA: 20,
      Clinic: 15,
      Nurse: 10,
      Doctor: 5,
    },
  ]

  return (
    <Card
      title="FTEs"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <CustomSelect
          value={viewType}
          setValue={setViewType}
          options={OPTIONS}
          disabled={isLoading}
        />
      }
      isLoading={isLoading}
    >
      <StackedBarChart data={data as unknown as StackedBarData[]} />
    </Card>
  )
}

export default FTEs
