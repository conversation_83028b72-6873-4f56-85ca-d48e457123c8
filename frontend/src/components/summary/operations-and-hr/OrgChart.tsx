"use client"

import React, { useState } from "react"
import dynamic from "next/dynamic"

import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { CustomSelect } from "@/components/ChartComponents"
import { useProfitAndLossRevenue } from "@/services/summary"

const Tree = dynamic(() => import("react-d3-tree"), { ssr: false })

const OPTIONS = ["By Department", "By Role"]

const OrgChart = () => {
  const { isLoading } = useProfitAndLossRevenue()
  const [viewType, setViewType] = useState<string>(OPTIONS[0])

  const orgChart = {
    name: "CEO",
    children: [
      {
        name: "Chief Medical Officer",
        children: [
          {
            name: "Head of Surgery",
            children: [{ name: "<PERSON><PERSON>" }, { name: "Surgical Nurse" }],
          },
          {
            name: "Head of Pediatrics",
            children: [{ name: "Pediatrician" }, { name: "Nurse" }],
          },
        ],
      },
      {
        name: "Chief Operating Officer",
        children: [
          {
            name: "HR Manager",
            children: [{ name: "Recruiter" }],
          },
          {
            name: "IT Manager",
            children: [{ name: "SysAd<PERSON>" }],
          },
        ],
      },
    ],
  }

  return (
    <Card
      title="Org Chart"
      filters={
        <CustomSelect
          value={viewType}
          setValue={setViewType}
          options={OPTIONS}
          disabled={isLoading}
        />
      }
      isLoading={false}
    >
      <div
        className="bg-accent/50 rounded-md"
        style={{
          height: CHART_HEIGHT,
        }}
      >
        <Tree
          data={orgChart}
          pathFunc="elbow"
          translate={{
            x: 150,
            y: CHART_HEIGHT / 2,
          }}
          zoom={0.5}
          nodeSize={{ x: 150, y: 75 }}
        />
      </div>
    </Card>
  )
}

export default OrgChart
