"use client"

import React, { useState } from "react"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { StackedBarData } from "@/types/summary"
import { Card } from "@/components/CardComponents"
import { ChartTypeSelect, CustomSelect } from "@/components/ChartComponents"
import { StackedBarChart } from "@/components/summary/operations-and-hr/Headcount"
import { useProfitAndLossRevenue } from "@/services/summary"

const OPTIONS = ["Default", "By Segment"]

const Clinics = () => {
  const { isLoading } = useProfitAndLossRevenue()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])
  const [viewType, setViewType] = useState<string>(OPTIONS[0])

  const data = [
    {
      name: "2021",
      "Women's Health": 35,
      Oncology: 20,
      Paediatric: 15,
      Imaging: 10,
      Others: 5,
    },
    {
      name: "2022",
      "Women's Health": 35,
      Oncology: 30,
      Paediatric: 10,
      Imaging: 30,
      Others: 5,
    },
    {
      name: "2023",
      "Women's Health": 35,
      Oncology: 30,
      Paediatric: 10,
      Imaging: 30,
      Others: 20,
    },
    {
      name: "2024",
      "Women's Health": 35,
      Oncology: 20,
      Paediatric: 15,
      Imaging: 10,
      Others: 10,
    },
    {
      name: "2025",
      "Women's Health": 35,
      Oncology: 20,
      Paediatric: 15,
      Imaging: 10,
      Others: 5,
    },
  ]

  return (
    <Card
      title="Clinics"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <>
          <CustomSelect
            value={viewType}
            setValue={setViewType}
            options={OPTIONS}
            disabled={isLoading}
          />

          <ChartTypeSelect
            value={chartType}
            setValue={setChartType}
            disabled={isLoading}
          />
        </>
      }
      isLoading={isLoading}
    >
      <StackedBarChart
        data={data as unknown as StackedBarData[]}
        chartType={chartType}
      />
    </Card>
  )
}

export default Clinics
