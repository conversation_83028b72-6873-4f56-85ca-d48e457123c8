"use client"

import React, { useState } from "react"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { Card } from "@/components/CardComponents"
import { ChartTypeSelect, CustomSelect } from "@/components/ChartComponents"
import { RevenueChart } from "@/components/summary/profit-and-loss/Revenue"
import { useProfitAndLossRevenue } from "@/services/summary"

const OPTIONS = ["Default", "By Segment"]

const Doctors = () => {
  const { data, isLoading } = useProfitAndLossRevenue()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])
  const [viewType, setViewType] = useState<string>(OPTIONS[0])

  return (
    <Card
      title="Doctors"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <>
          <CustomSelect
            value={viewType}
            setValue={setViewType}
            options={OPTIONS}
            disabled={isLoading}
          />

          <ChartTypeSelect
            value={chartType}
            setValue={setChartType}
            disabled={isLoading}
          />
        </>
      }
      isLoading={isLoading}
    >
      <RevenueChart data={data} chartType={chartType} />
    </Card>
  )
}

export default Doctors
