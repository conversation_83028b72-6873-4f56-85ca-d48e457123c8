"use client"

import React from "react"

import { formatCurrency } from "@/lib/number"
import {
  CustomLineChart,
  OverviewCard,
} from "@/components/summary/BusinessOverview"

const Overview = () => (
  <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4 xl:grid-cols-6">
    <OverviewCard
      title="Revenue per FTE"
      value={`$${formatCurrency(2000, 0)}`}
      amount={1.8}
      unit="%"
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="Revenue per doctor"
      value={`$${formatCurrency(1000, 0)}`}
      amount={-30}
      unit="%"
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="Employees"
      value={formatCurrency(432, 0)}
      amount={-30}
      unit="%"
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="Attrition Rate"
      value="15%"
      amount={-30}
      unit="%"
      bottomRightItem={<CustomLineChart />}
    />
  </div>
)

export default Overview
