"use client"

import React, { useState } from "react"
import {
  Bar,
  BarChart,
  Cell,
  LabelList,
  LabelProps,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  <PERSON>Axis,
} from "recharts"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { ProfitAndLossRevenueData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { cn } from "@/lib/utils"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  ChartTypeSelect,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useProfitAndLossRevenue } from "@/services/summary"

const Revenue = () => {
  const { data, isLoading } = useProfitAndLossRevenue()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  return (
    <Card
      title="Revenue"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      isLoading={isLoading}
    >
      <RevenueChart data={data} chartType={chartType} />
    </Card>
  )
}

export default Revenue

export const RevenueChart = ({
  data,
  chartType,
}: {
  data: ProfitAndLossRevenueData[]
  chartType: ChartType
}) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    {chartType === "Bar" ? (
      <BarChart data={data} margin={{ top: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Bar dataKey="amount" name="Amount" fill="var(--chart-1)">
          {data.map((_, index) => (
            <Cell key={`cell-${index}`} fill="var(--chart-1)" />
          ))}

          <LabelList
            dataKey="amount"
            position="center"
            fill="white"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />

          <LabelList
            dataKey="percentage"
            content={(props: LabelProps) =>
              renderPercentageLabel(chartType, props)
            }
          />
        </Bar>
      </BarChart>
    ) : (
      <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Line dataKey="amount" name="Amount" stroke="var(--chart-1)">
          <LabelList
            dataKey="amount"
            position="bottom"
            fill="var(--chart-1)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />

          <LabelList
            dataKey="percentage"
            content={(props: LabelProps) =>
              renderPercentageLabel(chartType, props)
            }
          />
        </Line>
      </LineChart>
    )}
  </ResponsiveContainer>
)

const renderPercentageLabel = (chartType: ChartType, props: LabelProps) => {
  const { x, y, width, value } = props

  if (!value) return null

  const posX = chartType === "Bar" ? Number(x) + Number(width) / 2 : Number(x)

  return (
    <g>
      <rect
        className={cn(Number(value) > 0 ? "fill-green-100" : "fill-red-100")}
        x={posX - 25}
        y={Number(y) - 32}
        width={50}
        height={20}
        rx={12}
        ry={12}
      />
      <text
        className={cn(
          "text-xs font-medium",
          Number(value) > 0 ? "fill-green-600" : "fill-red-600"
        )}
        x={posX}
        y={Number(y) - 21}
        textAnchor="middle"
        dominantBaseline="middle"
      >
        {Number(value) > 0 ? "+" : ""}
        {value}%
      </text>
    </g>
  )
}
