"use client"

import React, { useState } from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Label<PERSON>ist,
  LabelProps,
  Legend,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON>Axis,
} from "recharts"

import { ProfitAndLossRevenueBreakdownData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  CUSTOM_CHART_COLORS,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useProfitAndLossRevenueBreakdown } from "@/services/summary"

const CHART_TYPES = ["By Segment", "By Clinic"] as const
type ChartType = (typeof CHART_TYPES)[number]

const RevenueBreakdown = () => {
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  const { data, isLoading } = useProfitAndLossRevenueBreakdown({
    type: chartType.toLowerCase().replaceAll(" ", "-"),
  })

  return (
    <Card
      title="Revenue Breakdown"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      isLoading={isLoading}
    >
      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <BarChart
          data={data}
          margin={{
            top: 24,
          }}
        >
          <XAxis
            dataKey="name"
            tick={WrappedTick}
            tickLine={false}
            interval={0}
          />

          <Tooltip
            content={<WrappedTooltip />}
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
          />

          <Legend
            iconType="circle"
            iconSize={8}
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, top: 0 }}
          />

          {Object.keys(data?.[0] || {})
            .filter((key) => key !== "name" && key !== "total")
            .map((key, index) => (
              <Bar
                key={key}
                dataKey={key}
                name={key}
                stackId="a"
                fill={
                  index < 5
                    ? `var(--color-chart-${index + 1})`
                    : CUSTOM_CHART_COLORS[index - 5] || "var(--color-chart-1)"
                }
              >
                <LabelList
                  dataKey={key}
                  content={renderLabelWithPercentage(data, key)}
                />
              </Bar>
            ))}

          <Bar dataKey="" stackId="a" fill="transparent">
            <LabelList
              dataKey="name"
              content={(props: LabelProps) => {
                const { x, y, width, value } = props
                const item = data.find((d) => d.name === value)
                if (!item) return null

                return (
                  <g>
                    <text
                      className="text-sm"
                      x={Number(x) + Number(width) / 2}
                      y={Number(y) - 12}
                      textAnchor="middle"
                      dominantBaseline="middle"
                    >
                      {formatAbbreviatedCurrency(item.total)}
                    </text>
                  </g>
                )
              }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </Card>
  )
}

export default RevenueBreakdown

const ChartTypeSelect = ({
  value,
  setValue,
  disabled,
}: {
  value: ChartType
  setValue: React.Dispatch<React.SetStateAction<ChartType>>
  disabled?: boolean
}) => (
  <Select
    value={value}
    onValueChange={(val) => setValue(val as ChartType)}
    disabled={disabled}
  >
    <SelectTrigger size="sm">
      <SelectValue placeholder="Select" />
    </SelectTrigger>
    <SelectContent>
      {CHART_TYPES.map((type) => (
        <SelectItem key={type} value={type}>
          {type}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
)

const renderLabelWithPercentage = (
  data: ProfitAndLossRevenueBreakdownData[],
  dataKey: keyof ProfitAndLossRevenueBreakdownData
) => {
  const LabelComponent = (props: LabelProps) => {
    const { x, y, width = 0, height = 0, index, value } = props

    if (
      typeof index !== "number" ||
      typeof value !== "number" ||
      x === undefined ||
      y === undefined
    ) {
      return null
    }

    const item = data[index]
    const total = item.total

    if (!total || total === 0) return null

    const percentage = ((value / total) * 100).toFixed(0)

    return (
      <text
        x={Number(x) + Number(width) / 2}
        y={Number(y) + Number(height) / 2}
        fill="white"
        textAnchor="middle"
        dominantBaseline="middle"
        fontSize={12}
      >
        {formatAbbreviatedCurrency(value)} ({percentage}%)
      </text>
    )
  }
  LabelComponent.displayName = `LabelWithPercentage_${String(dataKey)}`

  return LabelComponent
}
