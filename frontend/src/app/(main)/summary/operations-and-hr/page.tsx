import React from "react"
import { cookies } from "next/headers"

import Notice from "@/components/layouts/Notice"
import Clinics from "@/components/summary/operations-and-hr/Clinics"
import Doctors from "@/components/summary/operations-and-hr/Doctors"
import FTEs from "@/components/summary/operations-and-hr/FTEs"
import Headcount from "@/components/summary/operations-and-hr/Headcount"
import OrgChart from "@/components/summary/operations-and-hr/OrgChart"
import Overview from "@/components/summary/operations-and-hr/Overview"

export const metadata = {
  title: "Operations & HR | Summary",
}

const OperationsAndHR = async () => {
  const cookieStore = await cookies()
  const noticeState = cookieStore.get("notice_state")?.value ?? "true"
  const defaultShow = noticeState === "true"

  return (
    <>
      <Overview />

      <h2 className="mt-2 -mb-1 flex flex-wrap items-baseline gap-x-4 gap-y-2 text-2xl leading-none font-medium">
        Operations & HR
        <span className="text-primary text-sm">
          Data snapshot as of 28 May 2025
        </span>
      </h2>

      <Notice defaultShow={defaultShow} />

      <div className="grid gap-4 lg:grid-cols-2">
        <Clinics />
        <Doctors />
        <FTEs />
        <Headcount />
        <OrgChart />
      </div>
    </>
  )
}

export default OperationsAndHR
