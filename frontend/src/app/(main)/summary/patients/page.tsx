import React from "react"
import { cookies } from "next/headers"

import Notice from "@/components/layouts/Notice"
import DigitalMKT from "@/components/summary/patients/DigitalMKT"
import NewPatients from "@/components/summary/patients/NewPatients"
import PatientsChart from "@/components/summary/patients/Patients"

export const metadata = {
  title: "Patients | Summary",
}

const Patients = async () => {
  const cookieStore = await cookies()
  const noticeState = cookieStore.get("notice_state")?.value ?? "true"
  const defaultShow = noticeState === "true"

  return (
    <>
      <h2 className="mt-2 -mb-1 flex flex-wrap items-baseline gap-x-4 gap-y-2 text-2xl leading-none font-medium">
        Patients
        <span className="text-primary text-sm">
          Data snapshot as of 28 May 2025
        </span>
      </h2>

      <Notice defaultShow={defaultShow} />

      <div className="grid gap-4 lg:grid-cols-2">
        <PatientsChart />
        <NewPatients />
        <DigitalMKT />
      </div>
    </>
  )
}

export default Patients
